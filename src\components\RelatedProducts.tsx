'use client';

import { useState, useEffect, useRef } from 'react';
import ProductCard from './ProductCard';
import { useCurrency } from '@/contexts/CurrencyContext';
import { useCartStore } from '@/store/useCartStore';
import { ShoppingBag, ChevronLeft, ChevronRight } from 'lucide-react';
import './RelatedProducts.css';

// Define Product type
type Product = {
  _id: string;
  name: string;
  description: {
    en: string;
    fr?: string;
    it?: string;
  } | string;
  price: number;
  category: {
    _id: string;
    name: string;
  } | string;
  imageUrl?: string;
  videoUrl?: string;
  createdAt: string;
  weight?: number;
}

type RelatedProductsProps = {
  currentProductIds: string[];
  limit?: number;
};

const RelatedProducts = ({ currentProductIds, limit = 8 }: RelatedProductsProps) => {
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false); // Changed to false initially
  const [isVisible, setIsVisible] = useState(false); // Track visibility
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isManualControl, setIsManualControl] = useState(false);
  const [isPaused, setIsPaused] = useState(false); // For hover pause
  const [cardsPerView, setCardsPerView] = useState(2); // Responsive cards count
  const { addItem } = useCartStore();
  const carouselRef = useRef<HTMLDivElement>(null);
  const autoScrollTimeoutRef = useRef<NodeJS.Timeout>();
  const observerRef = useRef<HTMLDivElement>(null); // For intersection observer
  const autoScrollIntervalRef = useRef<NodeJS.Timeout>(); // For auto-scroll timer

  // Touch/Swipe support
  const touchStartX = useRef<number>(0);
  const touchStartY = useRef<number>(0);
  const touchEndX = useRef<number>(0);
  const touchEndY = useRef<number>(0);
  const minSwipeDistance = 50;
  const isSwipingRef = useRef<boolean>(false);
  
  // Intersection Observer for lazy loading
  useEffect(() => {
    const observerElement = observerRef.current;
    if (!observerElement) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          setLoading(true);
        }
      },
      {
        rootMargin: '100px', // Start loading 100px before section is visible
        threshold: 0.1,
      }
    );

    observer.observe(observerElement);

    return () => {
      if (observerElement) {
        observer.unobserve(observerElement);
      }
    };
  }, [isVisible]);
  
  // Fetch related products only when visible
  useEffect(() => {
    if (!isVisible || !currentProductIds.length) return;

    const fetchRelatedProducts = async () => {
      try {
        console.log("Lazy loading related products for:", currentProductIds[0]);
        
        // Get the first product to find its category
        const productResponse = await fetch(`/api/products/${currentProductIds[0]}`);
        if (!productResponse.ok) {
          console.error('Failed to fetch product, status:', productResponse.status);
          throw new Error('Failed to fetch product');
        }
        
        const productData = await productResponse.json();
        console.log("Product data:", productData);
        
        if (!productData.success || !productData.product) {
          console.error('Invalid product data:', productData);
          throw new Error('Invalid product data');
        }
        
        const product = productData.product;
        
        // Extract the category ID regardless of whether it's a string or object
        let categoryId;
        if (product.category) {
          if (typeof product.category === 'string') {
            categoryId = product.category;
          } else if (typeof product.category === 'object' && product.category._id) {
            categoryId = product.category._id;
          }
        }
        
        if (!categoryId) {
          console.error('No valid category found for product:', product);
          setLoading(false);
          return;
        }
        
        console.log("Using category ID:", categoryId);
        
        // Fetch related products with the same category
        const relatedResponse = await fetch(`/api/products?category=${categoryId}`);
        if (!relatedResponse.ok) {
          console.error('Failed to fetch related products, status:', relatedResponse.status);
          throw new Error('Failed to fetch related products');
        }
        
        const relatedData = await relatedResponse.json();
        console.log("Related products data:", relatedData);
        
        if (!relatedData.success) {
          console.error('Invalid related products data:', relatedData);
          throw new Error('Invalid related products data');
        }
        
        // Filter out current products and limit the results
        const filteredProducts = relatedData.products
          .filter((p: Product) => !currentProductIds.includes(p._id))
          .slice(0, limit);
          
        console.log("Filtered related products:", filteredProducts.length);
        setRelatedProducts(filteredProducts);
      } catch (error) {
        console.error('Error fetching related products:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchRelatedProducts();
  }, [isVisible, currentProductIds, limit]);

  // Responsive cards per view detection
  useEffect(() => {
    const updateCardsPerView = () => {
      if (window.innerWidth >= 1024) {
        setCardsPerView(4); // Laptop: 4 cards
      } else if (window.innerWidth >= 768) {
        setCardsPerView(3); // Tablet: 3 cards
      } else {
        setCardsPerView(2); // Mobile: 2 cards
      }
    };

    updateCardsPerView();
    window.addEventListener('resize', updateCardsPerView);

    return () => window.removeEventListener('resize', updateCardsPerView);
  }, []);

  // Auto-scroll functionality
  useEffect(() => {
    if (!relatedProducts.length || isManualControl || isPaused) {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
      return;
    }

    autoScrollIntervalRef.current = setInterval(() => {
      setCurrentIndex((prev) => {
        const maxIndex = Math.max(0, relatedProducts.length - cardsPerView);
        return prev >= maxIndex ? 0 : prev + 1;
      });
    }, 1000); // Move every 1 second

    return () => {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
    };
  }, [relatedProducts.length, cardsPerView, isManualControl, isPaused]);

  // Auto-scroll resume after manual interaction
  useEffect(() => {
    if (isManualControl) {
      // Clear existing timeout
      if (autoScrollTimeoutRef.current) {
        clearTimeout(autoScrollTimeoutRef.current);
      }

      // Resume auto-scroll after 2 seconds of no interaction
      autoScrollTimeoutRef.current = setTimeout(() => {
        setIsManualControl(false);
      }, 2000);
    }

    return () => {
      if (autoScrollTimeoutRef.current) {
        clearTimeout(autoScrollTimeoutRef.current);
      }
    };
  }, [isManualControl, currentIndex]);
  
  const handlePrevious = () => {
    if (relatedProducts.length === 0) return;

    setIsManualControl(true);
    setCurrentIndex((prev) => {
      const maxIndex = Math.max(0, relatedProducts.length - cardsPerView);
      return prev <= 0 ? maxIndex : prev - 1;
    });
  };

  const handleNext = () => {
    if (relatedProducts.length === 0) return;

    setIsManualControl(true);
    setCurrentIndex((prev) => {
      const maxIndex = Math.max(0, relatedProducts.length - cardsPerView);
      return prev >= maxIndex ? 0 : prev + 1;
    });
  };

  // Calculate transform percentage based on screen size
  const getTransformPercentage = () => {
    const cardWidth = 100 / cardsPerView; // Each card's width percentage
    return currentIndex * cardWidth;
  };

  // Hover handlers for pause functionality
  const handleMouseEnter = () => {
    setIsPaused(true);
  };

  const handleMouseLeave = () => {
    setIsPaused(false);
  };

  // Touch event handlers for swipe support
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX;
    touchStartY.current = e.targetTouches[0].clientY;
    isSwipingRef.current = false;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStartX.current || !touchStartY.current) return;

    touchEndX.current = e.targetTouches[0].clientX;
    touchEndY.current = e.targetTouches[0].clientY;

    const deltaX = Math.abs(touchEndX.current - touchStartX.current);
    const deltaY = Math.abs(touchEndY.current - touchStartY.current);

    // If horizontal movement is greater than vertical movement
    if (deltaX > deltaY && deltaX > 10) {
      // This is a horizontal swipe - prevent page scroll
      isSwipingRef.current = true;
      e.preventDefault();
      e.stopPropagation();
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!touchStartX.current || !touchEndX.current) {
      // Reset values
      touchStartX.current = 0;
      touchStartY.current = 0;
      touchEndX.current = 0;
      touchEndY.current = 0;
      isSwipingRef.current = false;
      return;
    }

    const deltaX = touchStartX.current - touchEndX.current;
    const deltaY = Math.abs(touchStartY.current - touchEndY.current);
    const deltaXAbs = Math.abs(deltaX);

    // Only process if it's a horizontal swipe
    if (deltaXAbs > minSwipeDistance && deltaXAbs > deltaY) {
      e.preventDefault();
      e.stopPropagation();

      if (deltaX > 0) {
        // Left swipe - next
        handleNext();
      } else {
        // Right swipe - previous
        handlePrevious();
      }
    }

    // Reset touch positions
    touchStartX.current = 0;
    touchStartY.current = 0;
    touchEndX.current = 0;
    touchEndY.current = 0;
    isSwipingRef.current = false;
  };
  
  const handleBuyNow = (product: Product) => {
    addItem({
      product,
      quantity: 1
    });
  };
  
  // Show loading skeleton when not yet visible or loading
  if (!isVisible || loading) {
    return (
      <div ref={observerRef} id="related-products-main-container" className="mt-8">
        <div id="related-products-header-section" className="flex flex-col items-center mb-6">
          <div id="related-products-title-section" className="flex items-center gap-2 mb-2">
            <ShoppingBag className="text-[#D3821F]" size={20} />
            <h2 id="related-products-title" className="font-medium" style={{ fontFamily: 'var(--font-dosis), sans-serif', fontSize: '21.38px', lineHeight: '36px', letterSpacing: '2.4px' }}>Related Products</h2>
          </div>
          <p id="related-products-subtitle" className="text-gray-600 text-sm text-center" style={{ fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px' }}>Customers also viewed these products</p>
        </div>
        
        {/* Loading Skeleton */}
        <div id="related-products-skeleton" className="relative overflow-hidden">
          <div className="flex gap-4">
            {Array.from({ length: 2 }, (_, index) => (
              <div key={index} className="flex-shrink-0 w-[calc(50%-8px)] animate-pulse">
                <div className="aspect-square bg-gray-200 rounded-lg mb-3"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  if (!relatedProducts.length) {
    return (
      <div ref={observerRef} id="related-products-no-results" className="mt-8 text-center py-6">
        <p id="related-products-no-results-message" className="text-gray-500">No related products found at this time.</p>
        <p id="related-products-no-results-subtitle" className="text-sm text-gray-400 mt-2">Continue with your purchase or explore more products later.</p>
      </div>
    );
  }

  // No need for duplication in card-by-card navigation
  
  return (
    <div ref={observerRef} id="related-products-main-container" className="mt-8">
      <div id="related-products-header-section" className="flex flex-col items-center mb-6">
        <div id="related-products-title-section" className="flex items-center gap-2 mb-2">
          <ShoppingBag className="text-[#D3821F]" size={20} />
          <h2 id="related-products-title" className="font-medium" style={{ fontFamily: 'var(--font-dosis), sans-serif', fontSize: '21.38px', lineHeight: '36px', letterSpacing: '2.4px' }}>Related Products</h2>
        </div>
        <p id="related-products-subtitle" className="text-gray-600 text-sm text-center" style={{ fontFamily: 'var(--font-dosis), sans-serif', letterSpacing: '1px' }}>Customers also viewed these products</p>
      </div>
      
      <div className="relative">
        {/* Left Navigation Button */}
        <button
          id="related-products-prev-btn"
          onClick={handlePrevious}
          className="absolute left-2 top-2 z-10 bg-white/90 hover:bg-white shadow-lg rounded-full p-2 border border-gray-200 hover:border-[#D3821F]"
          aria-label="Previous products"
        >
          <ChevronLeft size={20} className="text-gray-600 hover:text-[#D3821F]" />
        </button>
        
        {/* Right Navigation Button */}
        <button
          id="related-products-next-btn"
          onClick={handleNext}
          className="absolute right-2 top-2 z-10 bg-white/90 hover:bg-white shadow-lg rounded-full p-2 border border-gray-200 hover:border-[#D3821F]"
          aria-label="Next products"
        >
          <ChevronRight size={20} className="text-gray-600 hover:text-[#D3821F]" />
        </button>
        
        {/* Carousel */}
        <div
          id="related-products-carousel"
          ref={carouselRef}
          className="flex gap-4 related-products-carousel"
          style={{
            transform: `translateX(-${getTransformPercentage()}%)`,
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {relatedProducts.map((product, index) => (
            <div
              key={`${product._id}-${index}`}
              className={`flex-shrink-0 ${
                cardsPerView === 4 ? 'w-[calc(25%-12px)]' :
                cardsPerView === 3 ? 'w-[calc(33.333%-10.67px)]' :
                'w-[calc(50%-8px)]'
              }`}
            >
              <ProductCard
                imageSrc={product.imageUrl ? `${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${product.imageUrl}` : '/placeholder.jpg'}
                productName={product.name}
                regularPrice={product.price}
                salePrice={product.price}
                weight={product.weight}
                alt={product.name}
                productId={product._id} // Pass real product ID
                onBuy={() => handleBuyNow(product)}
                href={`/product/${product._id}`}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RelatedProducts; 