/* Related Products Carousel Styles */

/* Card-by-card slide animation */
.related-products-carousel {
  will-change: transform;
  transition: transform 0.6s ease-in-out;
}

.related-products-carousel:hover {
  /* Animation will be paused via JavaScript */
}

/* Manual control mode - smooth transitions */
.related-products-manual {
  will-change: transform;
  transition: transform 0.5s ease-in-out;
}

/* Mobile Breakpoint (default) */
#related-products-carousel {
  max-width: 85%; /* Limit max-width for mobile */
  margin: 0 auto;
}

#related-products-main-container .flex {
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

#related-products-main-container .flex-shrink-0 {
  width: calc(50% - 1rem);
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .related-products-carousel {
    animation-duration: 25s;
  }

  #related-products-carousel {
    max-width: 90%; /* Limit max-width for tablet */
    margin: 0 auto;
  }

  #related-products-main-container .flex {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
  }

  #related-products-main-container .flex-shrink-0 {
    width: calc(33.333% - 1.25rem);
  }
}

/* Laptop Breakpoint (1024px) */
@media (min-width: 1024px) {
  #related-products-carousel {
    max-width: 100%; /* Use full width */
    margin: 0;
    padding: 0; /* Remove padding to fix spacing */
  }

  #related-products-main-container .flex {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  #related-products-main-container .flex-shrink-0 {
    width: calc(25% - 1.5rem);
  }

  /* Balance the container spacing properly */
  #related-products-main-container {
    padding: 0 3rem; /* Balanced left-right padding */
    max-width: 1200px; /* Limit max width */
    margin: 0 auto; /* Center the container */
  }

  /* Ensure navigation buttons are properly positioned */
  #related-products-main-container .relative {
    margin: 0 auto;
    max-width: 100%;
  }
}

/* Smooth scrolling performance */
.related-products-carousel,
.related-products-manual {
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  /* Prevent page scroll during touch */
  touch-action: pan-y pinch-zoom;
  -webkit-overflow-scrolling: touch;
}

/* Mobile touch optimization */
@media (max-width: 767px) {
  #related-products-carousel {
    touch-action: pan-y pinch-zoom;
    -webkit-overflow-scrolling: touch;
  }

  #related-products-main-container {
    touch-action: pan-y pinch-zoom;
    overflow: hidden;
  }
}